<template>
  <div
    class="de-json-table"
    @dblclick="editStatus && showEditor()"
  >
    <el-row
      style="height: 100%;"
      :style="cssVars"
    >
      <p
        v-show="!!title"
        ref="title"
        :style="titleStyle"
      >{{ title }}</p>
      <ux-grid
        :key="renderKey"
        ref="plxTable"
        size="mini"
        class="table-class"
        :span-method="spanMethod"
        stripe
        :style="tableStyle"
        :header-row-style="table_header_class"
        :row-style="getRowStyle"
        :columns="columns"
        :data="tableData"
      />
    </el-row>
    <el-dialog
      title="编辑"
      :visible.sync="dialogVisible"
      append-to-body
      width="50%"
    >
      <codemirror
        ref="myCm"
        :value="element.propValue"
        class="codemirror"
        :options="cmOption"
      />
      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="handleOk"
        >确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { post } from '@/api/dataset/dataset'
import { codemirror } from 'vue-codemirror'
import { DEFAULT_COLOR_CASE } from '@/views/chart/chart/chart'
import { isChange } from '@/utils/conditionUtil'
// 核心样式
import 'codemirror/lib/codemirror.css'
// 引入主题后还需要在 options 中指定主题才会生效
import 'codemirror/theme/solarized.css'
import 'codemirror/mode/javascript/javascript.js'
// require active-line.js
import 'codemirror/addon/selection/active-line.js'
// closebrackets
import 'codemirror/addon/edit/closebrackets.js'

export default {
  components: {
    codemirror
  },
  props: {
    propValue: {
      type: String,
      require: true
    },
    element: {
      type: Object
    },
    editMode: {
      type: String,
      require: false,
      default: 'preview'
    },
    active: {
      type: Boolean,
      require: false,
      default: false
    }

  },
  data() {
    return {
      dialogVisible: false,
      datasetId: '',
      jsonVal: '',
      columns: [],
      tableData: [],
      tableDataFn: null,
      spanMethodFn: null,
      title: '',
      titleStyle: {},
      renderKey: 1,
      isFirstLoad: true,
      scrollBarColor: DEFAULT_COLOR_CASE.tableScrollBarColor,
      table_header_class: {
        fontSize: '12px',
        // color: '#606266',
        // background: '#e8eaec',
        color: '#000',
        background: 'rgb(109, 154, 73)',
        height: '36px'
      },
      table_item_class: {
        fontSize: '12px',
        color: '#606266',
        background: '#ffffff',
        height: '36px'
      },
      table_item_class_stripe: {
        fontSize: '12px',
        color: '#606266',
        background: '#ffffff',
        height: '36px'
      },
      cssStyleParams: {
        borderColor: DEFAULT_COLOR_CASE.tableBorderColor,
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        whiteSpace: 'nowrap'
      },
      cmOption: {
        theme: 'solarized',
        mode: 'application/json', // 设置为 JSON 模式
        lineNumbers: true, // 显示行号
        tabSize: 2, // Tab 缩进大小
        indentWithTabs: false, // 使用空格缩进
        smartIndent: true, // 智能缩进
        matchBrackets: true, // 括号匹配
        autoCloseBrackets: true, // 自动闭合括号
        extraKeys: { // 快捷键
          'Ctrl-Space': 'autocomplete'
        }
      }
    }
  },
  computed: {
    editStatus() {
      return this.editMode === 'edit' && !this.mobileLayoutStatus
    },
    tableStyle() {
      return {
        width: '100%',
        '--scroll-bar-color': this.scrollBarColor,
        '--footer-font-color': this.table_header_class.color,
        '--footer-bg-color': this.table_header_class.background,
        '--footer-font-size': this.table_header_class.fontSize,
        '--footer-height': this.table_header_class.height
      }
    },
    cssVars() {
      return {
        '--color': this.cssStyleParams.borderColor,
        '--overflow': this.cssStyleParams.overflow,
        '--text-overflow': this.cssStyleParams.textOverflow,
        '--white-space': this.cssStyleParams.whiteSpace
      }
    },
    ...mapState([
      'mobileLayoutStatus'
    ]),
    cfilters() {
      if (!this.element.filters) return []
      return JSON.parse(JSON.stringify(this.element.filters))
    }
  },

  watch: {
    'element.propValue': {
      handler(val) {
        if (val) {
          const data = JSON.parse(val)
          this.title = data.title
          this.titleStyle = data.titleStyle || {}
          if (data.tableDataFn) {
            this.tableDataFn = new Function(`return ${data.tableDataFn}`)()
          }
          if (data.spanMethodFn) {
            this.spanMethodFn = new Function(`return ${data.spanMethodFn}`)()
          }
          this.columns = data.columns
          this.datasetId = data.datasetId
          if (this.isFirstLoad) {
            setTimeout(() => {
              this.getData()
            }, 500)
          } else {
            this.getData()
          }
        } else {
          this.title = ''
          this.columns = []
          this.tableDataFn = null
        }
        this.renderKey += 1
      },
      immediate: true
    },
    'cfilters': {
      handler: function(val1, val2) {
        if (isChange(val1, val2) && !this.isFirstLoad) {
          this.getData()
        }
      },
      deep: true
    }
  },
  methods: {
    spanMethod(param) {
      if (this.spanMethodFn) {
        return this.spanMethodFn(param)
      }
    },
    onCmReady() {
      console.log(this.$refs.myCm)
    },
    showEditor() {
      this.jsonVal = this.element.propValue
      this.dialogVisible = true
    },
    getRowStyle({ row, rowIndex }) {
      if (rowIndex % 2 !== 0) {
        return this.table_item_class_stripe
      } else {
        return this.table_item_class
      }
    },
    getData() {
      const filter = (this.element.filters || [])
      post('/dataset/table/sqlData', {
        id: this.datasetId,
        filter
      }).then(res => {
        this.tableData = this.tableDataFn ? this.tableDataFn(res.data.data) : res.data.data
        this.$forceUpdate()
        this.isFirstLoad = false
      })
    },
    // responseResetButton() {
    //   if (!this.cfilters?.length) {
    //     this.getData()
    //   }
    // },
    handleOk() {
      try {
        JSON.parse(this.$refs.myCm.content)
      } catch {
        this.$message.error('请输入JSON格式内容')
        return
      }
      // 校验JSON
      this.element.propValue = this.$refs.myCm.content
      this.$store.commit('canvasChange')
      this.dialogVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.de-json-table {
  width: 100%;
  height: 100%;
}
.table-class ::v-deep .body--wrapper {
  background: rgba(1, 1, 1, 0);
}

.table-class ::v-deep .elx-cell {
  max-height: none !important;
  line-height: normal !important;
}

.table-page-inner {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 100%;
  overflow: hidden;
}

.table-page {
  position: absolute;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 100%;
  overflow: hidden;
}

.page-style {
  margin-right: auto;
}

.total-style {
  flex: 1;
  font-size: 12px;
  color: #606266;
  white-space: nowrap;
}

.page-style ::v-deep .el-input__inner {
  height: 24px;
}

.page-style ::v-deep button {
  background: transparent !important;
}

.page-style ::v-deep li {
  background: transparent !important;
}
.table-class{
  ::-webkit-scrollbar-thumb {
    background: var(--scroll-bar-color);
  }
}
.table-class{
  scrollbar-color: var(--scroll-bar-color) transparent;
}

.table-class {
  ::v-deep .elx-table.border--full .elx-body--column,
  ::v-deep .elx-table.border--full .elx-footer--column,
  ::v-deep .elx-table.border--full .elx-header--column {
    background-image: linear-gradient(var(--color, #e8eaec), var(--color, #e8eaec)), linear-gradient(var(--color, #e8eaec), var(--color, #e8eaec)) !important;
  }
  ::v-deep .elx-table--border-line {
    border: 1px solid var(--color, #e8eaec) !important;
  }
  ::v-deep .elx-table .elx-table--header-wrapper .elx-table--header-border-line {
    border-bottom: 1px solid var(--color, #e8eaec) !important;
  }
  ::v-deep .elx-table .elx-table--footer-wrapper {
    border-top: 1px solid var(--color, #e8eaec) !important;
  }
  ::v-deep .elx-checkbox .elx-checkbox--label,
  ::v-deep .elx-radio .elx-radio--label,
  ::v-deep .elx-radio-button .elx-radio--label,
  ::v-deep .elx-table .elx-body--column.col--ellipsis:not(.col--actived) > .elx-cell,
  ::v-deep .elx-table .elx-footer--column.col--ellipsis:not(.col--actived) > .elx-cell,
  ::v-deep .elx-table .elx-header--column.col--ellipsis:not(.col--actived) > .elx-cell{
    overflow: var(--overflow, 'hidden');
    text-overflow: var(--text-overflow, 'ellipsis');
    white-space: var(--white-space, 'nowrap');
  }
  ::v-deep .elx-table--footer {
    color: var(--footer-font-color);
    background: var(--footer-bg-color);
    font-size: var(--footer-font-size);
    height: var(--footer-height);
  }
}

</style>
