<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 23.0.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="图层_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
     preserveAspectRatio="none meet" viewBox="0 0 480 300" style="enable-background:new 0 0 480 300;" xml:space="preserve">
<style type="text/css">
	.st0{fill:#1094E5;}
	.st1{opacity:0.25;}
	.st2{fill-rule:evenodd;clip-rule:evenodd;fill:#1094E5;}
	.st3{opacity:0.45;}
	.st4{opacity:0.6;}
	.st5{opacity:0.75;}
	.st6{opacity:0.25;fill-rule:evenodd;clip-rule:evenodd;fill:#1094E5;}
	.st7{opacity:0.45;fill-rule:evenodd;clip-rule:evenodd;fill:#1094E5;}
	.st8{opacity:0.6;fill-rule:evenodd;clip-rule:evenodd;fill:#1094E5;}
	.st9{opacity:0.75;fill-rule:evenodd;clip-rule:evenodd;fill:#1094E5;}
	.st10{fill-rule:evenodd;clip-rule:evenodd;fill:none;stroke:#1094E5;stroke-width:3;stroke-miterlimit:10;}
</style>
<g>
	<g>
		<g>
			<path class="st0" d="M467.7,298.5H2.5V25l12.1-13h101l9.8-10h353.1v282.9L467.7,298.5z M3.5,297.5h463.7l10.3-13V3H125.8L116,13
				h-101L3.5,25.4V297.5z"/>
		</g>
	</g>
</g>
<g class="st1">
	<polygon class="st2" points="71.4,1 62.4,10 70.4,10 79.4,1 	"/>
</g>
<g class="st3">
	<polygon class="st2" points="82.4,1 73.4,10 81.4,10 90.4,1 	"/>
</g>
<g class="st4">
	<polygon class="st2" points="93.4,1 84.4,10 92.4,10 101.4,1 	"/>
</g>
<g class="st5">
	<polygon class="st2" points="104.4,1 95.4,10 103.4,10 112.4,1 	"/>
</g>
<g>
	<polygon class="st2" points="115.4,1 106.4,10 114.4,10 123.4,1 	"/>
</g>
<g>
	<polygon class="st0" points="4.4,37.2 1.4,37.2 1.4,24.6 14,10.9 41.2,10.9 41.2,13.9 15.3,13.9 4.4,25.8 	"/>
</g>
<g>
	<polygon class="st0" points="20.8,299.5 1.3,299.5 1.3,282.8 4.3,282.8 4.3,296.5 20.8,296.5 	"/>
</g>
<g>
	<polygon class="st0" points="468.1,299.3 446.2,299.3 446.2,296.3 466.7,296.3 476.5,284 476.5,268.8 479.5,268.8 479.5,285 	"/>
</g>
<g>
	<polygon class="st0" points="479.5,15.2 476.5,15.2 476.5,3.6 464.2,3.6 464.2,0.6 479.5,0.6 	"/>
</g>
</svg>
