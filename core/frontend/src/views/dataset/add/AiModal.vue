<template>
  <el-dialog
    title="AI助手"
    :visible.sync="dialogVisible"
    width="1160px"
    :before-close="handleClose"
  >
    <iframe
      src="http://***********:8031/"
      width="100%"
      height="640px"
      frameborder="0"
    />
    <span
      slot="footer"
      class="dialog-footer"
    >
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button
        type="primary"
        @click="dialogVisible = false"
      >确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'DialogComponent',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  methods: { }
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
::v-deep .el-dialog {
  margin-top: 56px !important;
}
::v-deep .el-dialog__body {
  padding: 0 12px;
}
</style>
