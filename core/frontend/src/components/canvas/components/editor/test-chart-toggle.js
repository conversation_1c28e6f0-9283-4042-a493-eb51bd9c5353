/**
 * 图表类型切换功能测试
 * 
 * 这个文件包含了测试图表类型临时切换功能的示例代码
 */

// 模拟图表数据
const mockChartData = {
  id: 'test-chart-001',
  type: 'line',
  render: 'antv',
  title: '测试折线图',
  customAttr: JSON.stringify({
    label: {
      show: true,
      position: 'top',
      labelContent: ['quota']
    },
    color: {
      seriesColors: []
    },
    size: {
      lineWidth: 2,
      lineSymbolSize: 4
    }
  }),
  customStyle: JSON.stringify({
    legend: {
      show: true
    },
    xAxis: {
      show: true
    },
    yAxis: {
      show: true
    }
  }),
  senior: JSON.stringify({
    previewFuncCfg: {
      previewBarTools: ['export', 'detail', 'zoom', 'chartToggle']
    },
    functionCfg: {
      emptyDataStrategy: 'breakLine'
    }
  }),
  data: {
    data: [
      { field: '2023-01', value: 100, category: '销售额' },
      { field: '2023-02', value: 120, category: '销售额' },
      { field: '2023-03', value: 90, category: '销售额' },
      { field: '2023-04', value: 150, category: '销售额' }
    ]
  }
}

// 测试图表类型切换逻辑
function testChartTypeToggle() {
  console.log('=== 图表类型切换功能测试 ===')
  
  // 1. 测试初始状态
  console.log('1. 初始图表类型:', mockChartData.type)
  
  // 2. 模拟切换到柱状图
  const targetType = mockChartData.type === 'line' ? 'bar' : 'line'
  console.log('2. 目标图表类型:', targetType)
  
  // 3. 创建临时配置
  const tempChart = createTempChartConfig(mockChartData, targetType)
  console.log('3. 临时图表配置:', {
    type: tempChart.type,
    customAttr: JSON.parse(tempChart.customAttr),
    senior: JSON.parse(tempChart.senior)
  })
  
  // 4. 验证配置变化
  const originalCustomAttr = JSON.parse(mockChartData.customAttr)
  const tempCustomAttr = JSON.parse(tempChart.customAttr)
  
  console.log('4. 配置变化对比:')
  console.log('   - 标签位置:', originalCustomAttr.label.position, '->', tempCustomAttr.label.position)
  console.log('   - 空数据策略:', 
    JSON.parse(mockChartData.senior).functionCfg.emptyDataStrategy, 
    '->', 
    JSON.parse(tempChart.senior).functionCfg.emptyDataStrategy
  )
  
  return tempChart
}

// 创建临时图表配置的辅助函数
function createTempChartConfig(chart, targetType) {
  // 深拷贝当前图表配置
  const tempChart = JSON.parse(JSON.stringify(chart))
  
  // 更新图表类型
  tempChart.type = targetType
  
  // 应用新图表类型的默认配置
  applyChartTypeDefaults(tempChart, targetType)
  
  return tempChart
}

// 应用图表类型默认配置
function applyChartTypeDefaults(chart, type) {
  // 解析自定义属性和样式
  const customAttr = JSON.parse(chart.customAttr)
  const customStyle = JSON.parse(chart.customStyle)
  const senior = JSON.parse(chart.senior)
  
  // 根据图表类型设置默认配置
  if (chart.render === 'echarts') {
    customAttr.label.position = 'inside'
  } else {
    customAttr.label.position = 'middle'
  }
  
  if (type.includes('bar')) {
    customAttr.label.labelContent = ['quota']
    senior.functionCfg = senior.functionCfg || {}
    senior.functionCfg.emptyDataStrategy = 'ignoreData'
  } else if (type.includes('line')) {
    customAttr.label.position = 'top'
  }
  
  // 重置自定义颜色
  customAttr.color.seriesColors = []
  
  // 更新图表配置
  chart.customAttr = JSON.stringify(customAttr)
  chart.customStyle = JSON.stringify(customStyle)
  chart.senior = JSON.stringify(senior)
}

// 测试事件广播模拟
function testEventBroadcast(tempChart) {
  console.log('\n=== 事件广播测试 ===')
  
  const eventData = {
    type: 'tempTypeChange',
    viewId: 'test-view-001',
    viewInfo: tempChart,
    originalType: mockChartData.type,
    isTemporary: true
  }
  
  console.log('广播事件数据:', eventData)
  
  // 模拟 UserView 组件的处理
  handleTempTypeChange(eventData)
}

// 模拟 UserView 组件的处理方法
function handleTempTypeChange(param) {
  console.log('\n=== UserView 处理测试 ===')
  
  // 模拟更新图表
  const updatedChart = {
    type: param.viewInfo.type,
    customAttr: param.viewInfo.customAttr,
    customStyle: param.viewInfo.customStyle,
    senior: param.viewInfo.senior
  }
  
  console.log('图表更新结果:', {
    type: updatedChart.type,
    isTemporary: param.isTemporary,
    originalType: param.originalType
  })
  
  // 模拟缓存更新
  console.log('缓存数据已更新')
  
  // 模拟重新渲染
  console.log('图表重新渲染完成')
}

// 运行测试
if (typeof window !== 'undefined') {
  // 浏览器环境
  window.testChartTypeToggle = function() {
    const tempChart = testChartTypeToggle()
    testEventBroadcast(tempChart)
    console.log('\n测试完成！请检查控制台输出。')
  }
  
  console.log('图表类型切换功能测试已加载。')
  console.log('在浏览器控制台中运行 testChartTypeToggle() 来执行测试。')
} else {
  // Node.js 环境
  const tempChart = testChartTypeToggle()
  testEventBroadcast(tempChart)
}

export { testChartTypeToggle, createTempChartConfig, applyChartTypeDefaults }
