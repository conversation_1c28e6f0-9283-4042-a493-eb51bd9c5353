# 图表类型临时切换功能

## 功能概述

在预览模式下，用户可以临时切换 line（折线图）和 bar（柱状图）之间的图表类型，而不会影响原始图表配置或保存到后端。

## 实现原理

### 1. 核心组件

- **EditBar.vue**: 提供图表类型切换按钮和用户交互界面
- **UserView.vue**: 处理临时图表类型变化的渲染逻辑
- **ChartEdit.vue**: 提供图表类型切换的核心逻辑参考

### 2. 关键方法

#### EditBar.vue 中的方法：

```javascript
// 切换图表类型
toggleChartType() {
  // 保存原始图表类型
  if (!this.originalChartType) {
    this.originalChartType = this.chart.type
  }
  
  // 确定目标类型
  const targetType = this.chart.type === 'line' ? 'bar' : 'line'
  
  // 创建临时配置并广播变化
  const tempChart = this.createTempChartConfig(targetType)
  bus.$emit('view-in-cache', {
    type: 'tempTypeChange',
    viewId: this.element.propValue.viewId,
    viewInfo: tempChart,
    originalType: this.originalChartType,
    isTemporary: true
  })
}

// 恢复原始图表类型
resetTempChartType() {
  // 在退出预览模式或组件销毁时自动调用
}
```

#### UserView.vue 中的处理：

```javascript
// 处理临时图表类型变化
handleTempTypeChange(param) {
  // 更新图表类型和配置
  this.chart.type = param.viewInfo.type
  this.chart.customAttr = param.viewInfo.customAttr
  this.chart.customStyle = param.viewInfo.customStyle
  
  // 重新渲染图表
  this.mergeScale()
}
```

### 3. 事件流程

1. 用户在预览模式下点击图表切换按钮
2. `toggleChartType()` 方法被调用
3. 创建临时图表配置，应用新类型的默认设置
4. 通过 `bus.$emit('view-in-cache')` 广播变化
5. `UserView.vue` 接收事件并更新图表渲染
6. 退出预览模式时自动恢复原始图表类型

## 使用方法

### 1. 启用功能

确保图表的 `senior.previewFuncCfg.previewBarTools` 包含 `'chartToggle'`：

```javascript
// 在图表配置中
{
  senior: {
    previewFuncCfg: {
      previewBarTools: ['export', 'detail', 'zoom', 'chartToggle']
    }
  }
}
```

### 2. 支持的图表类型

- 仅支持 `line`（折线图）和 `bar`（柱状图）之间的切换
- 其他图表类型不显示切换按钮

### 3. 用户界面

- 切换按钮显示在预览模式的工具栏中
- 按钮图标根据当前类型动态变化（line ↔ bar）
- 鼠标悬停显示切换提示和临时状态说明
- 临时切换状态下按钮有脉冲动画效果

## 技术特点

### 1. 临时性
- 切换仅在预览模式下生效
- 不会修改原始图表配置
- 不会保存到后端数据库
- 退出预览模式自动恢复

### 2. 智能配置
- 自动应用目标图表类型的默认配置
- 处理标签位置、空数据策略等差异
- 重置自定义颜色配置

### 3. 用户体验
- 实时切换，无需刷新
- 清晰的视觉反馈
- 状态提示信息

## 扩展性

如需支持更多图表类型的切换，可以：

1. 修改 `chartToggleShow` 计算属性的条件判断
2. 扩展 `toggleChartType` 方法的类型映射逻辑
3. 完善 `applyChartTypeDefaults` 方法的配置处理

## 注意事项

- 功能仅在预览模式下可用
- 仅支持 line 和 bar 类型的双向切换
- 临时切换不会影响数据查询，仅改变渲染方式
- 组件销毁时会自动清理临时状态
