<template>
  <div style="width: 100%">
    <el-col>
      <el-form
        ref="functionForm"
        :model="functionForm"
        label-width="80px"
        size="mini"
      >
        <el-form-item
          label="工具栏功能"
          class="form-item"
        >
          <el-checkbox-group
            v-model="functionForm.previewBarTools"
            @change="changePreviewFuncCfg"
          >
            <el-checkbox label="export">导出</el-checkbox>
            <el-checkbox label="detail">详情</el-checkbox>
            <el-checkbox label="zoom">放大</el-checkbox>
            <el-checkbox label="chartToggle">图表切换</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
    </el-col>
  </div>
</template>

<script>
import { DEFAULT_PREVIEW_FUNC_CFG } from '../../chart/chart'
import { cloneDeep } from 'lodash'

export default {
  name: 'PreviewFuncCfg',
  props: {
    chart: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      functionForm: JSON.parse(JSON.stringify(DEFAULT_PREVIEW_FUNC_CFG))
    }
  },
  computed: {

  },
  watch: {
    'chart': {
      handler: function() {
        this.initData()
      }
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    initData() {
      const chart = JSON.parse(JSON.stringify(this.chart))
      if (chart.senior) {
        let senior = null
        if (Object.prototype.toString.call(chart.senior) === '[object Object]') {
          senior = JSON.parse(JSON.stringify(chart.senior))
        } else {
          senior = JSON.parse(chart.senior)
        }
        const defaultPreviewFuncCfg = cloneDeep(DEFAULT_PREVIEW_FUNC_CFG)

        if (senior.previewFuncCfg) {
          this.functionForm = { ...defaultPreviewFuncCfg, ...senior.previewFuncCfg }
        } else {
          this.functionForm = JSON.parse(JSON.stringify(defaultPreviewFuncCfg))
        }
      }
    },
    changePreviewFuncCfg() {
      this.$emit('onPreviewFuncCfgChange', this.functionForm)
    }
  }
}
</script>

<style scoped lang="scss">
.shape-item{
  padding: 6px;
  border: none;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.form-item-slider ::v-deep .el-form-item__label{
  font-size: 12px;
  line-height: 38px;
}
.form-item ::v-deep .el-form-item__label{
  font-size: 12px;
}
.el-select-dropdown__item{
  padding: 0 20px;
}
span{
  font-size: 12px
}
.el-form-item{
  margin-bottom: 6px;
}

.switch-style{
  position: absolute;
  right: 10px;
  margin-top: -4px;
}
.color-picker-style{
  cursor: pointer;
  z-index: 1003;
}
.form-item ::v-deep .el-radio-group{
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  label {
    line-height: 28px;
  }
}
</style>
