<script>
import { mapState } from 'vuex'
import { post } from '@/api/dataset/dataset'
import { codemirror } from 'vue-codemirror'
import { isChange } from '@/utils/conditionUtil'
// 核心样式
import 'codemirror/lib/codemirror.css'
// 引入主题后还需要在 options 中指定主题才会生效
import 'codemirror/theme/solarized.css'
import 'codemirror/mode/javascript/javascript.js'
// require active-line.js
import 'codemirror/addon/selection/active-line.js'
// closebrackets
import 'codemirror/addon/edit/closebrackets.js'

export default {
  components: {
    codemirror
  },
  props: {
    propValue: {
      type: String,
      require: true
    },
    element: {
      type: Object
    },
    editMode: {
      type: String,
      require: false,
      default: 'preview'
    },
    active: {
      type: Boolean,
      require: false,
      default: false
    }

  },
  data() {
    return {
      dialogVisible: false,
      datasetId: '',
      jsonVal: '',
      title: '',
      titleStyle: {},
      context: {},
      renderKey: 1,
      comp: null,
      render: null,
      isFirstLoad: true,
      cmOption: {
        theme: 'solarized',
        mode: 'application/json', // 设置为 JSON 模式
        lineNumbers: true, // 显示行号
        tabSize: 2, // Tab 缩进大小
        indentWithTabs: false, // 使用空格缩进
        smartIndent: true, // 智能缩进
        matchBrackets: true, // 括号匹配
        autoCloseBrackets: true, // 自动闭合括号
        extraKeys: { // 快捷键
          'Ctrl-Space': 'autocomplete'
        }
      }
    }
  },
  computed: {
    editStatus() {
      return this.editMode === 'edit' && !this.mobileLayoutStatus
    },
    ...mapState([
      'mobileLayoutStatus'
    ]),
    cfilters() {
      if (!this.element.filters) return []
      return JSON.parse(JSON.stringify(this.element.filters))
    }
  },

  watch: {
    'element.propValue': {
      handler(val) {
        if (val) {
          const data = JSON.parse(val)
          if (data.cssStyles) {
            const styleId = 'de-prop-comp-' + this.element.id
            let styleElement = document.getElementById(styleId)
            if (styleElement) {
              styleElement.textContent = data.cssStyles
            } else {
              styleElement = document.createElement('style')
              styleElement.setAttribute('type', 'text/css')
              styleElement.id = styleId
              styleElement.textContent = data.cssStyles
              document.head.appendChild(styleElement)
            }
          }
          this.title = data.title
          this.titleStyle = data.titleStyle || {}
          this.datasetId = data.datasetId
          if (data.renderFn) {
            const render = new Function(`return ${data.renderFn}`)()
            this.render = render
          }
          if (this.isFirstLoad) {
            setTimeout(() => {
              this.getData()
            }, 500)
          } else {
            this.getData()
          }
        } else {
          this.title = ''
          this.columns = []
          this.tableDataFn = null
        }
        this.renderKey += 1
      },
      immediate: true
    },
    'cfilters': {
      handler: function(val1, val2) {
        if (isChange(val1, val2) && !this.isFirstLoad) {
          this.getData()
        }
      },
      deep: true
    }
  },
  methods: {
    onCmReady() {
      console.log('ready')
      console.log(this.$refs.myCm)
    },
    showEditor() {
      this.jsonVal = this.element.propValue
      this.dialogVisible = true
    },
    handleOk() {
      try {
        JSON.parse(this.$refs.myCm.content)
      } catch {
        this.$message.error('请输入JSON格式内容')
        return
      }
      // 校验JSON
      this.element.propValue = this.$refs.myCm.content
      this.$store.commit('canvasChange')
      this.dialogVisible = false
    },
    getData() {
      const filter = (this.element.filters || [])
      post('/dataset/table/sqlData', {
        id: this.datasetId,
        filter
      }).then(res => {
        this.comp = this.render({ h: this.$createElement, data: res.data.data })
        this.$forceUpdate()
        this.isFirstLoad = false
      })
    }
    // responseResetButton() {
    //   if (!this.cfilters?.length) {
    //     this.getData()
    //   }
    // }
  },
  render(h) {
    return h('div', {
      class: 'de-json-table',
      on: {
        dblclick: () => this.editStatus && this.showEditor()
      }
    }, [
      this.title && h('p', {
        style: this.titleStyle
      }, this.title),
      // 动态组件
      this.comp || null,

      // el-dialog 组件
      h('el-dialog', {
        props: {
          title: '编辑',
          visible: this.dialogVisible,
          appendToBody: true,
          width: '50%'
        },
        on: {
          'update:visible': (value) => { this.dialogVisible = value } // 处理 visible.sync
        }
      }, [
        // 对话框内容：codemirror
        h('codemirror', {
          ref: 'myCm',
          props: {
            value: this.element.propValue,
            options: this.cmOption
          },
          class: 'codemirror'
        }),
        h('div', { style: ' margin-top: 20px' }, [
          h('el-button', {
            on: {
              click: () => { this.dialogVisible = false }
            }
          }, '取 消'),
          h('el-button', {
            props: {
              type: 'primary'
            },
            on: {
              click: this.handleOk
            }
          }, '确 定')
        ])
      ])
    ])
  }
}
</script>

<style lang="scss" scoped>
.de-prop-table {
  width: 100%;
  height: 100%;
}
</style>
