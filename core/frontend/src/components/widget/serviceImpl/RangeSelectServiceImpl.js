import { WidgetService } from '../service/WidgetService'
import dayjs from 'dayjs'
const leftPanel = {
  icon: 'iconfont icon-riqi',
  label: '聚合选择',
  defaultClass: 'time-filter'
}

const dialogPanel = {
  options: {
    attrs: {
      analysisType: 'monthrange',
      viewIds: [],
      fieldId: '',
      parameters: [],
      dragItems: []
    },
    value: '',
    manualModify: false
  },
  defaultClass: 'time-filter',
  component: 'de-range'
}
const drawPanel = {
  type: 'custom',
  style: {
    width: 500,
    // height: 47,
    height: 90,
    fontSize: 14,
    fontWeight: 500,
    lineHeight: '',
    letterSpacing: 0,
    textAlign: '',
    color: ''
  },
  component: 'de-date'
}

class RangeSelectServiceImpl extends WidgetService {
  constructor(options = {}) {
    Object.assign(options, { name: 'rangeSelectWidget' })
    super(options)
    this.filterDialog = true
    this.showSwitch = false
  }

  initLeftPanel() {
    const value = JSON.parse(JSON.stringify(leftPanel))
    return value
  }

  initFilterDialog() {
    const value = JSON.parse(JSON.stringify(dialogPanel))
    return value
  }
  customValue() {
    return 2
  }

  initDrawPanel() {
    const value = JSON.parse(JSON.stringify(drawPanel))
    return value
  }

  filterFieldMethod(fields) {
    return fields.filter(field => {
      return field['deType'] === 1
    })
  }
  defaultSetting() {
    return dialogPanel.options.attrs.default
  }
  getParam(element, val) {
    const analysisType = element.options.attrs.analysisType
    const unit = ({
      'yearrange': 'year',
      'quarterrange': 'month',
      'monthrange': 'month'
    })[analysisType]
    const operator = 'aggregation-' + analysisType
    if (val) {
      return {
        component: element,
        value: val,
        operator
      }
    } else {
      const start = dayjs().startOf('year')
      const end = dayjs()
      const param = {
        component: element,
        value: [
          dayjs(start).startOf(unit).valueOf(),
          dayjs(end).endOf(unit).valueOf()
        ],
        operator
      }
      return param
    }
  }
  isParamWidget() {
    return true
  }
}
const rangeSelectServiceImpl = new RangeSelectServiceImpl()
export default rangeSelectServiceImpl
